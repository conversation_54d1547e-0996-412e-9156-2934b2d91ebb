package org.dromara.common.dubbo.config;

import org.apache.dubbo.common.constants.CommonConstants;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.core.Ordered;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import java.util.regex.Pattern;

/**
 * dubbo自定义IP注入(避免IP不正确问题)
 *
 * <AUTHOR> Li
 */
public class CustomBeanFactoryPostProcessor implements BeanFactoryPostProcessor, Ordered {

    /**
     * 获取该 BeanFactoryPostProcessor 的顺序，确保它在容器初始化过程中具有最高优先级
     *
     * @return 优先级顺序值，越小优先级越高
     */
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    /**
     * 在 Spring 容器初始化过程中对 Bean 工厂进行后置处理
     *
     * @param beanFactory 可配置的 Bean 工厂
     * @throws BeansException 如果在处理过程中发生错误
     */
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        String property = System.getProperty(CommonConstants.DubboProperty.DUBBO_IP_TO_REGISTRY);
        if (StringUtils.isNotBlank(property)) {
            if (isWildcardPattern(property)) {
                // 通配符模式匹配逻辑
                List<String> allIPs = getAllNetworkInterfaceIPs();
                String matchedIP = findMatchingIP(property, allIPs);
                if (matchedIP != null) {
                    System.setProperty("DUBBO_IP_TO_REGISTRY", matchedIP);
                    System.setProperty(CommonConstants.DubboProperty.DUBBO_IP_TO_REGISTRY, matchedIP);
                    return;
                }
                // 如果没有匹配到，继续执行原有的自动检测逻辑
            } else {
                // 非通配符，直接返回（保持原有行为）
                return;
            }
        }
        // 获取 InetUtils bean，用于获取 IP 地址
        InetUtils inetUtils = beanFactory.getBean(InetUtils.class);
        String ip = "127.0.0.1";
        // 获取第一个非回环地址
        InetAddress address = inetUtils.findFirstNonLoopbackAddress();
        if (address != null) {
            if (address instanceof Inet6Address) {
                // 处理 IPv6 地址
                String ipv6AddressString = address.getHostAddress();
                if (ipv6AddressString.contains("%")) {
                    // 去掉可能存在的范围 ID
                    ipv6AddressString = ipv6AddressString.substring(0, ipv6AddressString.indexOf("%"));
                }
                ip = ipv6AddressString;
            } else {
                // 处理 IPv4 地址
                ip = inetUtils.findFirstNonLoopbackHostInfo().getIpAddress();
            }
        }
        // 设置系统属性 DUBBO_IP_TO_REGISTRY 为获取到的 IP 地址
        System.setProperty("DUBBO_IP_TO_REGISTRY", ip);
        System.setProperty(CommonConstants.DubboProperty.DUBBO_IP_TO_REGISTRY, ip);
    }

    /**
     * 检测IP字符串是否包含通配符
     *
     * @param ip IP字符串
     * @return true表示包含通配符
     */
    private boolean isWildcardPattern(String ip) {
        return ip != null && (ip.contains("*") || ip.contains("?"));
    }

    /**
     * 将通配符模式转换为正则表达式
     *
     * @param wildcardPattern 通配符模式，如 "100.*"
     * @return 正则表达式字符串
     */
    private String wildcardToRegex(String wildcardPattern) {
        // 对于IP地址通配符：
        // * 表示匹配剩余的IP地址部分（如100.*匹配100.x.x.x）
        // ? 表示匹配一个IP段（1-3位数字）

        StringBuilder regex = new StringBuilder();
        char[] chars = wildcardPattern.toCharArray();

        for (char c : chars) {
            if (c == '*') {
                // * 匹配剩余的IP地址部分
                regex.append(".*");
            } else if (c == '?') {
                // ? 匹配一个IP段（1-3位数字）
                regex.append("\\d{1,3}");
            } else if (c == '.') {
                // 点号需要转义
                regex.append("\\.");
            } else {
                // 普通字符直接添加
                regex.append(c);
            }
        }

        return regex.toString();
    }

    /**
     * 获取本机所有网络接口的IP地址
     *
     * @return IP地址列表
     */
    private List<String> getAllNetworkInterfaceIPs() {
        List<String> ipList = new ArrayList<>();
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            for (NetworkInterface networkInterface : Collections.list(networkInterfaces)) {
                // 跳过非活动和回环接口
                if (!networkInterface.isUp() || networkInterface.isLoopback()) {
                    continue;
                }

                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                for (InetAddress inetAddress : Collections.list(inetAddresses)) {
                    // 只处理IPv4地址，跳过回环和链路本地地址
                    if (!inetAddress.isLoopbackAddress() &&
                        !inetAddress.isLinkLocalAddress() &&
                        !(inetAddress instanceof Inet6Address)) {
                        ipList.add(inetAddress.getHostAddress());
                    }
                }
            }
        } catch (SocketException e) {
            // 网络接口获取失败时，返回空列表，让系统使用默认逻辑
            return Collections.emptyList();
        }
        return ipList;
    }

    /**
     * 在IP列表中找到第一个匹配模式的IP
     *
     * @param pattern 通配符模式
     * @param ipList  IP地址列表
     * @return 匹配的IP地址，如果没有匹配则返回null
     */
    private String findMatchingIP(String pattern, List<String> ipList) {
        try {
            String regex = wildcardToRegex(pattern);
            Pattern compiledPattern = Pattern.compile(regex);

            for (String ip : ipList) {
                if (compiledPattern.matcher(ip).matches()) {
                    return ip;
                }
            }
        } catch (Exception e) {
            // 正则表达式编译失败或匹配异常时，返回null
            return null;
        }
        return null;
    }
}

