package org.dromara.wallet.monitor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.context.event.EventListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;

/**
 * 抽象入账事件处理器基类
 * 提供通用的事件处理流程，包括异步处理、重试机制、错误恢复和状态管理
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>统一的异步事件处理机制</li>
 *   <li>标准化的重试和恢复策略</li>
 *   <li>通用的租户上下文管理</li>
 *   <li>一致的日志记录和错误处理</li>
 *   <li>支持泛型，适配不同的交易模型和配置类型</li>
 * </ul>
 *
 * <p>设计原则：</p>
 * <ul>
 *   <li>模板方法模式，提供统一的事件处理流程</li>
 *   <li>抽象方法由子类实现链特定的业务逻辑</li>
 *   <li>通用方法提供可复用的功能</li>
 *   <li>保持各链特有功能的完整性</li>
 * </ul>
 *
 * @param <T> 交易模型类型（如 TronTransactionModel、EvmTransactionModel）
 * @param <C> 配置门面类型（如 TronConfigFacade、EvmConfigFacade）
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractDepositEventHandler<T, C> {

    /**
     * 处理入账接收事件
     * 异步执行业务逻辑处理，支持自动重试
     *
     * @param event 入账接收事件
     */
    @EventListener
    @Async
    @Retryable(
        retryFor = {Exception.class},
        backoff = @Backoff(delay = 2000, multiplier = 2),
        maxAttempts = 3
    )
    public void handleDepositReceivedEvent(TransactionReceivedEvent<T, C> event) {
        // 设置租户上下文
        TenantHelper.setDynamic("000000", true);

        T transaction = event.getTransaction();
        C configFacade = event.getConfigFacade();
        String chainName = getChainName(configFacade);

        log.info("{}链开始异步处理入账事件: {}", chainName, event.getFormattedInfo());

        try {
            // 验证事件有效性
            if (!event.isValid()) {
                log.warn("{}链收到无效的入账事件，跳过处理", chainName);
                return;
            }

            // 只处理接收交易，其他类型交易已在TransactionManager中直接保存完成
            String transactionType = getTransactionType(transaction);
            if (!"receive".equals(transactionType)) {
                log.warn("{}链入账事件处理器收到非接收交易，跳过处理: type={}, hash={}",
                    chainName, transactionType, getTransactionHash(transaction));
                return;
            }

            // 增加重试次数
            incrementRetryCount(transaction);

            // 执行接收交易的完整业务逻辑处理
            handleReceiveTransactionLogic(transaction, configFacade);

            // 处理成功，更新状态为已完成
            updateTransactionStatus(transaction, configFacade, 1, null);

            log.info("{}链接收入账事件处理成功: {}", chainName, getTransactionHash(transaction));

        } catch (Exception e) {
            log.error("{}链处理入账事件失败: {}, 错误信息: {}",
                chainName, getTransactionHash(transaction), e.getMessage(), e);
            throw e; // 重新抛出异常，触发重试机制
        }
    }

    /**
     * 重试失败后的恢复处理
     * 当@Retryable重试次数用尽后，会调用此方法进行最终处理
     *
     * @param e     最后一次重试的异常
     * @param event 交易接收事件
     */
    @Recover
    public void recoverTransactionProcessing(Exception e, TransactionReceivedEvent<T, C> event) {
        T transaction = event.getTransaction();
        C configFacade = event.getConfigFacade();
        String chainName = getChainName(configFacade);

        log.error("{}链交易{}在{}次重试后最终失败，错误信息: {}",
            chainName,
            getTransactionHash(transaction),
            getRetryCount(transaction),
            e.getMessage(), e);

        // 记录最终失败的详细信息
        String errorMessage = String.format("重试%d次后最终失败: %s",
            getRetryCount(transaction), e.getMessage());

        try {
            // 更新交易状态为最终失败
            updateTransactionStatus(transaction, configFacade, 4, errorMessage);

            // 发送失败告警
            sendFailureAlert(transaction, configFacade, errorMessage);

            log.error("{}链交易{}已标记为最终失败状态", chainName, getTransactionHash(transaction));

        } catch (Exception updateException) {
            log.error("{}链交易{}状态更新失败: {}",
                chainName, getTransactionHash(transaction), updateException.getMessage(), updateException);
        }
    }

    // ============ 抽象方法 - 由子类实现 ============

    /**
     * 处理接收交易的业务逻辑
     * 由子类实现具体的业务处理逻辑
     *
     * @param transaction  交易模型
     * @param configFacade 配置门面
     */
    protected abstract void handleReceiveTransactionLogic(T transaction, C configFacade);

    /**
     * 更新交易状态
     * 由子类实现具体的状态更新逻辑
     *
     * @param transaction    交易模型
     * @param configFacade   配置门面
     * @param status         新状态
     * @param errorMessage   错误信息（可为null）
     */
    protected abstract void updateTransactionStatus(T transaction, C configFacade, int status, String errorMessage);

    /**
     * 获取链名称
     * 由子类实现具体的链名称获取逻辑
     *
     * @param configFacade 配置门面
     * @return 链名称
     */
    protected abstract String getChainName(C configFacade);

    /**
     * 获取交易哈希
     * 由子类实现具体的交易哈希获取逻辑
     *
     * @param transaction 交易模型
     * @return 交易哈希
     */
    protected abstract String getTransactionHash(T transaction);

    /**
     * 获取交易类型
     * 由子类实现具体的交易类型获取逻辑
     *
     * @param transaction 交易模型
     * @return 交易类型
     */
    protected abstract String getTransactionType(T transaction);

    /**
     * 获取重试次数
     * 由子类实现具体的重试次数获取逻辑
     *
     * @param transaction 交易模型
     * @return 重试次数
     */
    protected abstract int getRetryCount(T transaction);

    /**
     * 增加重试次数
     * 由子类实现具体的重试次数增加逻辑
     *
     * @param transaction 交易模型
     */
    protected abstract void incrementRetryCount(T transaction);

    // ============ 通用方法 - 可被子类覆盖 ============

    /**
     * 发送失败告警
     * 默认实现记录日志，子类可以覆盖实现具体的告警逻辑
     *
     * @param transaction    交易模型
     * @param configFacade   配置门面
     * @param errorMessage   错误信息
     */
    protected void sendFailureAlert(T transaction, C configFacade, String errorMessage) {
        String chainName = getChainName(configFacade);
        String txHash = getTransactionHash(transaction);
        
        log.warn("{}链交易{}处理最终失败，需要人工介入: {}", chainName, txHash, errorMessage);
        
        // 子类可以覆盖此方法实现具体的告警逻辑
        // 例如：发送邮件、短信、钉钉通知等
    }

    /**
     * 验证交易是否可以处理
     * 默认实现基础验证，子类可以覆盖实现具体的验证逻辑
     *
     * @param transaction  交易模型
     * @param configFacade 配置门面
     * @return 是否可以处理
     */
    protected boolean canProcessTransaction(T transaction, C configFacade) {
        if (transaction == null) {
            log.warn("{}链交易对象为空，无法处理", getChainName(configFacade));
            return false;
        }

        String transactionType = getTransactionType(transaction);
        if (transactionType == null || transactionType.trim().isEmpty()) {
            log.warn("{}链交易{}类型为空，无法处理", 
                getChainName(configFacade), getTransactionHash(transaction));
            return false;
        }

        return true;
    }

    /**
     * 记录处理开始
     *
     * @param transaction  交易模型
     * @param configFacade 配置门面
     */
    protected void logProcessingStart(T transaction, C configFacade) {
        String chainName = getChainName(configFacade);
        String txHash = getTransactionHash(transaction);
        String transactionType = getTransactionType(transaction);
        int retryCount = getRetryCount(transaction);
        
        log.info("{}链开始处理{}交易: hash={}, 重试次数={}", 
            chainName, transactionType, txHash, retryCount);
    }

    /**
     * 记录处理完成
     *
     * @param transaction  交易模型
     * @param configFacade 配置门面
     */
    protected void logProcessingComplete(T transaction, C configFacade) {
        String chainName = getChainName(configFacade);
        String txHash = getTransactionHash(transaction);
        String transactionType = getTransactionType(transaction);
        
        log.info("{}链{}交易处理完成: hash={}", chainName, transactionType, txHash);
    }

    /**
     * 获取事件处理摘要
     *
     * @param event 事件对象
     * @return 处理摘要
     */
    protected String getEventSummary(TransactionReceivedEvent<T, C> event) {
        if (!event.isValid()) {
            return "Invalid event";
        }
        
        T transaction = event.getTransaction();
        C configFacade = event.getConfigFacade();
        
        return String.format("%s链事件[type=%s, hash=%s]",
            getChainName(configFacade),
            getTransactionType(transaction),
            getTransactionHash(transaction));
    }
}
