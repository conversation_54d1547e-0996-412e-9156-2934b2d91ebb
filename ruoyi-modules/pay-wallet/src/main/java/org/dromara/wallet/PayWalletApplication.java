package org.dromara.wallet;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 区块链钱包
 *
 * <AUTHOR>
 */
//@EnableDubbo
@SpringBootApplication
@EnableScheduling
public class PayWalletApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(PayWalletApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  区块链钱包模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
