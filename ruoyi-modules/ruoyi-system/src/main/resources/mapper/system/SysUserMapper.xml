<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysUserMapper">

    <resultMap type="org.dromara.system.domain.vo.SysUserVo" id="SysUserResult">
        <id property="userId" column="user_id"/>
    </resultMap>
    <resultMap type="org.dromara.system.domain.vo.SysUserExportVo" id="SysUserExportResult">
        <id property="userId" column="user_id"/>
    </resultMap>

    <select id="selectPageUserList" resultMap="SysUserResult">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            u.user_id, u.dept_id, u.uuid, u.nick_name, u.user_name, u.email, u.country_code, u.avatar, u.google_secret_key, u.phonenumber, u.sex,
            u.status, u.del_flag, u.login_ip, u.login_date, u.location, u.os, u.device, u.create_by, u.create_time, u.remark,
            u.invite_code, u.login_limit_expired, u.trade_limit_expired, u.language
        </if>
        from sys_user u
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserList" resultMap="SysUserResult">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            u.user_id, u.dept_id, u.uuid, u.nick_name, u.user_name, u.email, u.country_code, u.avatar, u.google_secret_key, u.phonenumber, u.sex,
            u.status, u.del_flag, u.login_ip, u.login_date, u.location, u.os, u.device, u.create_by, u.create_time, u.remark,
            u.invite_code, u.login_limit_expired, u.trade_limit_expired, u.language
        </if>
        from sys_user u
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserExportList" resultMap="SysUserExportResult">
        select u.user_id, u.dept_id, u.uuid, u.nick_name, u.user_name, u.email, u.country_code, u.avatar, u.phonenumber, u.sex,
               u.status, u.del_flag, u.login_ip, u.login_date, u.location, u.create_by, u.create_time, u.remark,
               u.invite_code, u.language, d.dept_name, d.leader, u1.user_name as leaderName
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user u1 on u1.user_id = d.leader
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectAllocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUnallocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="countUserById" resultType="Long">
        select count(*) from sys_user where del_flag = '0' and user_id = #{userId}
    </select>

    <select id="queryPageList" resultType="org.dromara.system.domain.SysUser">
        select t1.user_id as userId, t1.user_name as username, t1.nick_name as nickname, t1.phonenumber, t1.status,
               t1.create_time as createTime, t1.login_limit_expired as loginLimitExpired, t1.trade_limit_expired as tradeLimitExpired,
               t2.user_name as channelName
        from sys_user t1 left join sys_user t2 on t1.user_id = t2.channel_user_id
        where t1.user_type != '00' and t1.del_flag != '2'
        <if test="bo.nickName != null and bo.nickName != ''">
            and t1.nick_name = #{bo.nickName}
        </if>
        <if test="bo.status != null and bo.status != ''">
            and t1.status = #{bo.status}
        </if>
        <if test="bo.createTime != null and bo.createTime != ''">
            and t1.create_time &gt;= #{bo.createTime}
        </if>
        <if test="bo.endDate != null and bo.endDate != ''">
            and t1.create_time &lt;= #{bo.endDate}
        </if>
        <if test="bo.limitFlag != null and bo.limitFlag != '' and bo.limitFlag == '1'">
            and (t1.login_limit_expired > now() or t1.trade_limit_expired > now())
        </if>
        <if test="bo.username != null and bo.username != ''">
            and t1.user_name like concat('%', #{bo.username}, '%')
        </if>
        <if test="bo.channelName != null and bo.channelName != ''">
            and t2.user_name like concat('%', #{bo.channelName}, '%')
        </if>
        order by t1.create_time desc
    </select>


</mapper>
