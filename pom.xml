<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-cloud-plus</artifactId>
    <version>${revision}</version>

    <name>RuoYi-Cloud-Plus</name>
    <url>https://gitee.com/dromara/RuoYi-Cloud-Plus</url>
    <description>Dromara RuoYi-Cloud-Plus微服务系统</description>

    <properties>
        <revision>2.4.0</revision>
        <websocket.version>1.5.3</websocket.version>
        <web3j.version>4.9.5</web3j.version>
        <solanaj.version>1.20.4</solanaj.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <spring-boot.version>3.4.6</spring-boot.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <spring-boot-admin.version>3.4.7</spring-boot-admin.version>
        <mybatis.version>3.5.16</mybatis.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <dynamic-ds.version>4.3.1</dynamic-ds.version>
        <velocity.version>2.3</velocity.version>
        <swagger.core.version>2.2.30</swagger.core.version>
        <springdoc.version>2.8.8</springdoc.version>
        <therapi-javadoc.version>0.15.0</therapi-javadoc.version>
        <fastexcel.version>1.2.0</fastexcel.version>
        <hutool.version>5.8.35</hutool.version>
        <redisson.version>3.45.1</redisson.version>
        <lock4j.version>2.2.7</lock4j.version>
        <snailjob.version>1.5.0</snailjob.version>
        <satoken.version>1.42.0</satoken.version>
        <lombok.version>1.18.36</lombok.version>
        <logstash.version>7.4</logstash.version>
        <easy-es.version>2.1.0</easy-es.version>
        <skywalking-toolkit.version>9.3.0</skywalking-toolkit.version>
        <tron.version>0.10.0</tron.version>
        <bouncycastle.version>1.80</bouncycastle.version>
        <mapstruct-plus.version>1.4.8</mapstruct-plus.version>
        <mapstruct-plus.lombok.version>0.2.0</mapstruct-plus.lombok.version>
        <justauth.version>1.16.7</justauth.version>
        <!-- 离线IP地址定位库 -->
        <ip2region.version>2.7.0</ip2region.version>
        <!-- 临时修复 fastjson 漏洞 -->
        <fastjson.version>1.2.83</fastjson.version>

        <!-- OSS 配置 -->
        <aws.sdk.version>2.28.22</aws.sdk.version>

        <!-- SMS 配置 -->
        <sms4j.version>3.3.4</sms4j.version>
        <!-- 面向运行时的D-ORM依赖 -->
        <anyline.version>8.7.2-20250101</anyline.version>
        <!-- 工作流配置 -->
        <warm-flow.version>1.7.3</warm-flow.version>
        <!-- mq配置 -->
        <rocketmq.version>2.3.0</rocketmq.version>

        <!-- 插件版本 -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
        <!-- 打包默认跳过测试 -->
        <skipTests>true</skipTests>
        <mockito.version>5.14.2</mockito.version>
    </properties>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.server>*************:8848</nacos.server>
<!--                <nacos.server>*************:8848</nacos.server>-->
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <logstash.address>*************:4560</logstash.address>
<!--                <logstash.address>*************:4560</logstash.address>-->
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
<!--        <profile>-->
<!--            <id>test</id>-->
<!--            <properties>-->
<!--                <profiles.active>test</profiles.active>-->
<!--                <nacos.server>*************:8848</nacos.server>-->
<!--                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>-->
<!--                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>-->
<!--                <nacos.username>nacos</nacos.username>-->
<!--                <nacos.password>nacos</nacos.password>-->
<!--                <logstash.address>*************:4560</logstash.address>-->
<!--            </properties>-->
<!--        </profile>-->
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.server>*************:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <logstash.address>*************:4560</logstash.address>
            </properties>
        </profile>
    </profiles>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.java-websocket</groupId>
                <artifactId>Java-WebSocket</artifactId>
                <version>${websocket.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/io.github.tronprotocol/trident -->
            <dependency>
                <groupId>io.github.tronprotocol</groupId>
                <artifactId>trident</artifactId>
                <version>${tron.version}</version>
            </dependency>
            <!--sol SDK-->
            <dependency>
                <groupId>com.mmorrell</groupId>
                <artifactId>solanaj</artifactId>
                <version>${solanaj.version}</version>
            </dependency>

            <!--eth SDK-->
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>core</artifactId>
                <version>${web3j.version}</version>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-alibaba-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- JustAuth 的依赖配置-->
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- api 的依赖配置-->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-api-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.therapi</groupId>
                <artifactId>therapi-runtime-javadoc</artifactId>
                <version>${therapi-javadoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!--  SnailJob Client -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-starter</artifactId>
                <version>${snailjob.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-job-core</artifactId>
                <version>${snailjob.version}</version>
            </dependency>

            <!-- 加密包引入 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <!-- logstash -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>

            <!-- skywalking 整合 logback -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking-toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking-toolkit.version}</version>
            </dependency>

            <!--  AWS SDK for Java 2.x  -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3-transfer-manager</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!-- 将基于 Netty 的 HTTP 客户端从类路径中移除 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>netty-nio-client</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>

            <!--短信sms4j-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>

            <!-- 离线IP地址定位库 ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- dubbo 专用 -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>5.1.0</version>
            </dependency>

            <dependency>
                <groupId>io.github.linpeilie</groupId>
                <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
                <version>${mapstruct-plus.version}</version>
            </dependency>

            <!--消息队列-->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <!-- Warm-Flow国产工作流引擎, 在线文档：http://warm-flow.cn/ -->
            <dependency>
                <groupId>org.dromara.warm</groupId>
                <artifactId>warm-flow-mybatis-plus-sb3-starter</artifactId>
                <version>${warm-flow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.warm</groupId>
                <artifactId>warm-flow-plugin-ui-sb-web</artifactId>
                <version>${warm-flow.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ruoyi-auth</module>
        <module>ruoyi-gateway</module>
        <module>ruoyi-visual</module>
        <module>ruoyi-modules</module>
        <module>ruoyi-api</module>
        <module>ruoyi-common</module>
        <module>ruoyi-example</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>0.15.0</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                        <path>
                            <groupId>io.github.linpeilie</groupId>
                            <artifactId>mapstruct-plus-processor</artifactId>
                            <version>${mapstruct-plus.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${mapstruct-plus.lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- 单元测试使用 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <!-- 根据打包环境执行对应的@Tag测试方法 -->
                    <groups>${profiles.active}</groups>
                    <!-- 排除标签 -->
                    <excludedGroups>exclude</excludedGroups>
                </configuration>
            </plugin>
            <!-- 统一版本号管理 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp/</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>logback*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>huawei nexus</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>huawei nexus</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>

